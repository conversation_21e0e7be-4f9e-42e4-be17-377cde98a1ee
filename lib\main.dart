import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:realtracker/screens/login_screen.dart';




Future <void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Supabase.initialize(
    url: 'https://ypqrgvicycfbgnrbgeoq.supabase.co', // Replace with your Supabase URL
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlwcXJndmljeWNmYmducmJnZW9xIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyMjE1NDUsImV4cCI6MjA2NTc5NzU0NX0.M4ciBn7s1f9fAxFNpBC_auYrhm_DCmemMxoA64ToEhs', // Replace with your Supabase anon key
  );

  runApp(MyApp());
}


class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Expense Tracker',
      theme: ThemeData(primarySwatch: Colors.teal),
      home: const LoginScreen(), // home screen
    );
  }
}
