import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/transaction.dart';

class TransactionService {
  final SupabaseClient _client = Supabase.instance.client;
  static const String _tableName = 'transactions';

  // Get all transactions for the current user
  Future<List<Transaction>> getTransactions() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final response = await _client
          .from(_tableName)
          .select()
          .eq('user_id', userId)
          .order('date', ascending: false);

      return response.map((json) => Transaction.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to fetch transactions: $e');
    }
  }

  // Get transactions filtered by type
  Future<List<Transaction>> getTransactionsByType(TransactionType type) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final typeString = type == TransactionType.income ? 'income' : 'expense';
      final response = await _client
          .from(_tableName)
          .select()
          .eq('user_id', userId)
          .eq('type', typeString)
          .order('date', ascending: false);

      return response.map((json) => Transaction.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to fetch transactions: $e');
    }
  }

  // Add a new transaction
  Future<Transaction> addTransaction(Transaction transaction) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final transactionData = transaction.toInsertJson();
      transactionData['user_id'] = userId;

      final response = await _client
          .from(_tableName)
          .insert(transactionData)
          .select()
          .single();

      return Transaction.fromJson(response);
    } catch (e) {
      throw Exception('Failed to add transaction: $e');
    }
  }

  // Update an existing transaction
  Future<Transaction> updateTransaction(Transaction transaction) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final response = await _client
          .from(_tableName)
          .update({
            'description': transaction.description,
            'category': transaction.category,
            'amount': transaction.amount,
            'type': transaction.type == TransactionType.income ? 'income' : 'expense',
            'date': transaction.date.toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', transaction.id)
          .eq('user_id', userId)
          .select()
          .single();

      return Transaction.fromJson(response);
    } catch (e) {
      throw Exception('Failed to update transaction: $e');
    }
  }

  // Delete a transaction
  Future<void> deleteTransaction(String transactionId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      await _client
          .from(_tableName)
          .delete()
          .eq('id', transactionId)
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('Failed to delete transaction: $e');
    }
  }

  // Get transaction summary (total income, total expenses, net profit)
  Future<TransactionSummary> getTransactionSummary() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final transactions = await getTransactions();
      
      double totalIncome = 0;
      double totalExpenses = 0;

      for (final transaction in transactions) {
        if (transaction.type == TransactionType.income) {
          totalIncome += transaction.amount;
        } else {
          totalExpenses += transaction.amount;
        }
      }

      return TransactionSummary(
        totalIncome: totalIncome,
        totalExpenses: totalExpenses,
        netProfit: totalIncome - totalExpenses,
        transactionCount: transactions.length,
      );
    } catch (e) {
      throw Exception('Failed to get transaction summary: $e');
    }
  }

  // Get transactions for a specific date range
  Future<List<Transaction>> getTransactionsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final response = await _client
          .from(_tableName)
          .select()
          .eq('user_id', userId)
          .gte('date', startDate.toIso8601String())
          .lte('date', endDate.toIso8601String())
          .order('date', ascending: false);

      return response.map((json) => Transaction.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to fetch transactions: $e');
    }
  }
}

class TransactionSummary {
  final double totalIncome;
  final double totalExpenses;
  final double netProfit;
  final int transactionCount;

  TransactionSummary({
    required this.totalIncome,
    required this.totalExpenses,
    required this.netProfit,
    required this.transactionCount,
  });

  @override
  String toString() {
    return 'TransactionSummary(totalIncome: $totalIncome, totalExpenses: $totalExpenses, netProfit: $netProfit, transactionCount: $transactionCount)';
  }
}
