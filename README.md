# Business Tracker

A cross-platform business income and expense tracking application built with Flutter and Supabase.

## Features

### Phase 1 (Current)
- ✅ User authentication (sign up, sign in, sign out)
- ✅ Basic app structure with navigation
- ✅ Add income and expense transactions
- ✅ View transactions list with filtering
- ✅ Dashboard with summary cards
- ✅ Clean, modern Material 3 UI

### Planned Features
- Transaction categories management
- Data visualization (charts and graphs)
- Export functionality (PDF, CSV)
- Multi-business support
- Recurring transactions
- Receipt photo attachments
- Advanced reporting and analytics

## Platforms

This app is designed to run on:
- 📱 Android
- 🍎 iOS
- 🖥️ Windows Desktop

## Tech Stack

- **Frontend**: Flutter (Dart)
- **Backend**: Supabase
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: Supabase Auth
- **State Management**: Built-in Flutter state management

## Getting Started

### Prerequisites

- Flutter SDK (3.8.1 or higher)
- Dart SDK
- Supabase account

### Setup

1. Clone the repository
2. Install dependencies:
   ```bash
   flutter pub get
   ```

3. Set up Supabase:
   - Create a new project at [supabase.com](https://supabase.com)
   - Get your project URL and anon key
   - Update `lib/main.dart` with your Supabase credentials

4. Run the app:
   ```bash
   flutter run
   ```

## Project Structure

```
lib/
├── main.dart                 # App entry point
├── models/                   # Data models
│   └── transaction.dart
├── screens/                  # UI screens
│   ├── auth/
│   │   └── login_screen.dart
│   ├── home/
│   │   └── home_screen.dart
│   └── transactions/
│       ├── add_transaction_screen.dart
│       └── transactions_list_screen.dart
└── services/                 # Business logic
    └── transaction_service.dart
```

## Database Schema

The app uses the following Supabase tables:

### transactions
- `id` (uuid, primary key)
- `user_id` (uuid, foreign key to auth.users)
- `description` (text)
- `category` (text)
- `amount` (numeric)
- `type` (text: 'income' or 'expense')
- `date` (timestamp)
- `created_at` (timestamp)
- `updated_at` (timestamp)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
