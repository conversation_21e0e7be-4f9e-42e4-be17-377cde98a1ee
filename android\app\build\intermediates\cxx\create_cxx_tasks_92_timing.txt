# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 11ms
    create-variant-model 12ms
    create-ARMEABI_V7A-model 26ms
    create-X86-model 12ms
    create-module-model 39ms
    create-variant-model 25ms
    create-ARMEABI_V7A-model 14ms
    create-ARM64_V8A-model 11ms
    create-X86-model 54ms
    create-X86_64-model 20ms
    create-module-model 19ms
    create-ARMEABI_V7A-model 25ms
    create-ARM64_V8A-model 16ms
    create-X86-model 19ms
    [gap of 11ms]
  create-initial-cxx-model completed in 354ms
  [gap of 16ms]
create_cxx_tasks completed in 370ms

